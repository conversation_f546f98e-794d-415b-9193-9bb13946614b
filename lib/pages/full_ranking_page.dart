import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../theme/app_design_system.dart';
import '../models/user.dart';
import '../services/ai_data_service.dart';
import '../components/top3_ranking_card.dart';
import '../pages/ai_detail_page.dart';

/// Full Ranking Page
/// 显示完整的AI角色排行榜，包含前10名用户
class FullRankingPage extends StatefulWidget {
  const FullRankingPage({super.key});

  @override
  State<FullRankingPage> createState() => _FullRankingPageState();
}

class _FullRankingPageState extends State<FullRankingPage>
    with TickerProviderStateMixin {
  List<User> _top10Users = [];
  bool _isLoading = true;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late List<AnimationController> _itemControllers;
  late List<Animation<double>> _itemAnimations;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _loadRankingData();
  }

  void _initAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    // 为排行榜列表项创建动画控制器
    _itemControllers = List.generate(7, (index) => AnimationController(
      duration: Duration(milliseconds: 400 + (index * 100)),
      vsync: this,
    ));

    _itemAnimations = _itemControllers.map((controller) => 
      Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeOutBack)
      )
    ).toList();
  }

  void _startAnimations() {
    _animationController.forward();
    
    // 依次启动每个列表项的动画
    for (int i = 0; i < _itemControllers.length; i++) {
      Future.delayed(Duration(milliseconds: 600 + (i * 100)), () {
        if (mounted) {
          _itemControllers[i].forward();
        }
      });
    }
  }

  Future<void> _loadRankingData() async {
    try {
      final users = await AIDataService().getTop10WeeklyUsers();
      if (mounted) {
        setState(() {
          _top10Users = users;
          _isLoading = false;
        });
        _startAnimations();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    for (var controller in _itemControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppDesignSystem.backgroundPrimary,
      appBar: _buildAppBar(context),
      body: _isLoading ? _buildLoadingState() : _buildContent(context),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: AppDesignSystem.backgroundPrimary,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_ios, color: AppDesignSystem.textPrimary),
        onPressed: () => Navigator.of(context).pop(),
      ),
      title: Text(
        AppLocalizations.of(context)!.aiCharacterRankings,
        style: const TextStyle(
          color: AppDesignSystem.textPrimary,
          fontSize: 18,
          fontWeight: AppDesignSystem.fontWeightBold,
        ),
      ),
      centerTitle: true,
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(AppDesignSystem.primaryYellow),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    if (_top10Users.isEmpty) {
      return _buildEmptyState(context);
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: CustomScrollView(
        slivers: [
          // Top 3 颁奖台
          _buildTop3Section(),
          
          // 第4-10名列表
          _buildRankingList(),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.emoji_events_outlined,
            size: 64,
            color: AppDesignSystem.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            AppLocalizations.of(context)!.noData,
            style: TextStyle(
              color: AppDesignSystem.textSecondary,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection() {
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.all(16),
        child: Row(
          children: [
            // All characters 按钮
            _buildFilterChip(
              AppLocalizations.of(context)!.allCharacters,
              isSelected: true,
            ),
            const SizedBox(width: 12),
            // Fictional 按钮
            _buildFilterChip(
              AppLocalizations.of(context)!.fictional,
              isSelected: false,
            ),
            const Spacer(),
            // New 标签
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppDesignSystem.primaryYellow,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                AppLocalizations.of(context)!.newLabel,
                style: const TextStyle(
                  color: AppDesignSystem.backgroundPrimary,
                  fontSize: 12,
                  fontWeight: AppDesignSystem.fontWeightBold,
                ),
              ),
            ),
            const SizedBox(width: 12),
            // Filters 按钮
            Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(20),
                onTap: () {
                  // TODO: 实现筛选功能
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(AppLocalizations.of(context)!.filters),
                      duration: const Duration(seconds: 1),
                    ),
                  );
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppDesignSystem.borderSecondary),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.tune,
                        size: 16,
                        color: AppDesignSystem.textSecondary,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        AppLocalizations.of(context)!.filters,
                        style: TextStyle(
                          color: AppDesignSystem.textSecondary,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterChip(String label, {required bool isSelected}) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(20),
        onTap: () {
          // TODO: 实现筛选功能
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('筛选: $label'),
              duration: const Duration(seconds: 1),
            ),
          );
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: isSelected ? AppDesignSystem.backgroundPrimary : Colors.transparent,
            border: Border.all(
              color: isSelected ? AppDesignSystem.textPrimary : AppDesignSystem.borderSecondary,
            ),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            label,
            style: TextStyle(
              color: isSelected ? AppDesignSystem.textPrimary : AppDesignSystem.textSecondary,
              fontSize: 14,
              fontWeight: isSelected ? AppDesignSystem.fontWeightMedium : AppDesignSystem.fontWeightRegular,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTop3Section() {
    if (_top10Users.length < 3) return const SliverToBoxAdapter(child: SizedBox.shrink());
    
    final top3 = _top10Users.take(3).toList();
    
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        child: Top3RankingCard(
          top3Users: top3,
        ),
      ),
    );
  }

  Widget _buildRankingList() {
    if (_top10Users.length <= 3) return const SliverToBoxAdapter(child: SizedBox.shrink());
    
    final remainingUsers = _top10Users.skip(3).toList();
    
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          final user = remainingUsers[index];
          final rank = index + 4; // 从第4名开始
          return _buildRankingItem(context, user, rank);
        },
        childCount: remainingUsers.length,
      ),
    );
  }

  Widget _buildRankingItem(BuildContext context, User user, int rank) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () => _navigateToUserDetail(context, user),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppDesignSystem.backgroundSecondary,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppDesignSystem.borderSecondary.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                // 排名数字
                Container(
                  width: 32,
                  height: 32,
                  child: Center(
                    child: Text(
                      rank.toString(),
                      style: TextStyle(
                        color: _getRankColor(rank),
                        fontSize: 18,
                        fontWeight: AppDesignSystem.fontWeightBold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),

                // 用户头像
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24),
                    border: Border.all(
                      color: _getRankColor(rank).withValues(alpha: 0.3),
                      width: 2,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(22),
                    child: Image.asset(
                      user.avatar,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: AppDesignSystem.backgroundSecondary,
                          child: Icon(
                            Icons.person,
                            color: AppDesignSystem.textSecondary,
                            size: 24,
                          ),
                        );
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 16),

                // 用户信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user.name,
                        style: const TextStyle(
                          color: AppDesignSystem.textPrimary,
                          fontSize: 16,
                          fontWeight: AppDesignSystem.fontWeightBold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        user.description,
                        style: TextStyle(
                          color: AppDesignSystem.textSecondary,
                          fontSize: 14,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),

                // 评分和查看按钮
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.star,
                          color: AppDesignSystem.primaryYellow,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          user.score.toStringAsFixed(1),
                          style: const TextStyle(
                            color: AppDesignSystem.textPrimary,
                            fontSize: 16,
                            fontWeight: AppDesignSystem.fontWeightBold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        border: Border.all(color: AppDesignSystem.borderSecondary),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        AppLocalizations.of(context)!.view,
                        style: TextStyle(
                          color: AppDesignSystem.textSecondary,
                          fontSize: 12,
                          fontWeight: AppDesignSystem.fontWeightMedium,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getRankColor(int rank) {
    switch (rank) {
      case 4:
      case 5:
        return Colors.purple;
      case 6:
      case 7:
        return Colors.blue;
      default:
        return Colors.green;
    }
  }

  void _navigateToUserDetail(BuildContext context, User user) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AIDetailPage(aiUser: user),
      ),
    );
  }
}
